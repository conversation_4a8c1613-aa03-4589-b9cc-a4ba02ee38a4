# === view/annotation_view.py ===
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from PIL import Image, ImageTk
import cv2
import numpy as np
from typing import List, Tuple, Optional, Dict
import logging
import os
from config.constants import LABEL_COLORS_HEX, MASK_COLORS_BGR
from services.mask_calculation_service import MaskCalculationService


class AnnotationView:
    """
    Vue principale de l'application d'annotation.
    Gère l'interface utilisateur et les interactions graphiques.
    """
    
    # Constantes de classe
    WINDOW_TITLE = "Polygon Mask Editor"
    DEFAULT_THRESHOLD = 150
    MIN_ZOOM = 0.2
    MAX_ZOOM = 10.0
    ZOOM_FACTOR = 1.1
    POINT_RADIUS = 3
    DASH_PATTERN = (4, 2)
    
    # Utiliser les constantes centralisées
    LABEL_COLORS = LABEL_COLORS_HEX
    
    # Configuration de la police
    FONT_CONFIG = ("Arial", 12, "bold")
    
    def __init__(self, root: tk.Tk, controller):
        """
        Initialise la vue avec la fenêtre principale et le contrôleur.

        Args:
            root: Fenêtre principale Tkinter
            controller: Instance du contrôleur
        """
        self._root = root
        self._controller = controller
        self._logger = logging.getLogger(__name__)
        self._root.title(self.WINDOW_TITLE)
        self._last_directory = os.getcwd()  # Utiliser le chemin absolu du dossier courant

        # Services
        self._mask_service = MaskCalculationService()

        # Variables d'interface (reflètent les paramètres du label actuel)
        self._threshold_var = tk.IntVar(value=150)
        self._alpha_var = tk.DoubleVar(value=50)
        self._mask_type_var = tk.StringVar(value="polygon")
        self._smooth_contours_var = tk.BooleanVar(value=False)
        self._drawing_mode_var = tk.StringVar(value="polygon")
        self._vertical_gap_var = tk.IntVar(value=5)  # Nouveau paramètre pour la distance verticale

        # Ajouter des traces sur les variables pour la mise à jour en temps réel
        self._threshold_var.trace_add('write', self._on_threshold_var_change)
        self._alpha_var.trace_add('write', self._on_alpha_var_change)

        # Variables pour le mode rectangle
        self._rectangle_start = None
        self._rectangle_preview = None
        self._mouse_pos = None
        self._zoom = 1.0
        self._offset = [0, 0]
        self._current_polygon: List[Tuple[int, int]] = []
        self._image: Optional[np.ndarray] = None
        self._original_image: Optional[np.ndarray] = None  # Image originale sans overlay
        self._tk_image: Optional[ImageTk.PhotoImage] = None
        self._cached_mask: Optional[np.ndarray] = None  # Cache du masque calculé
        self._cached_mask_key: Optional[str] = None  # Clé du cache pour vérifier la validité
        self._update_pending = False  # Flag pour éviter les mises à jour multiples

        # Note: Les paramètres sont maintenant liés aux polygones individuels,
        # pas aux labels. Les variables d'interface représentent les paramètres
        # actuels pour les nouveaux polygones.

        self._create_menu()
        self._build_ui()
        self._bind_events()
        self._logger.info("Interface graphique initialisée")

    def _create_menu(self) -> None:
        """Crée la barre de menu."""
        menubar = tk.Menu(self._root)
        
        # Menu Fichier
        file_menu = tk.Menu(menubar, tearoff=0)
        file_menu.add_command(label="Ouvrir un dossier", command=self._open_folder)
        file_menu.add_separator()
        file_menu.add_command(label="Charger annotations JSON", command=self._load_json_annotations)
        file_menu.add_command(label="Exporter les masques", command=self._export_masks)
        file_menu.add_command(label="Créer des overlays", command=self._create_overlays)
        file_menu.add_separator()
        file_menu.add_command(label="Quitter", command=self._root.quit)
        menubar.add_cascade(label="Fichier", menu=file_menu)
        
        # Menu Édition
        edit_menu = tk.Menu(menubar, tearoff=0)
        edit_menu.add_command(label="Annuler le dernier polygone", command=self._undo_polygon)
        edit_menu.add_command(label="Changer de label (Espace)", command=self._switch_label)
        menubar.add_cascade(label="Édition", menu=edit_menu)
        
        self._root.config(menu=menubar)

    def _create_overlays(self) -> None:
        """Gère la création des overlays."""
        try:
            # Demander le dossier des images
            image_folder = filedialog.askdirectory(
                title="Sélectionner le dossier des images",
                initialdir=os.path.dirname(self._controller._model.image_list[0]) if self._controller._model.image_list else None
            )
            if not image_folder:
                return

            # Demander le dossier des masques
            mask_folder = filedialog.askdirectory(
                title="Sélectionner le dossier des masques",
                initialdir=os.path.dirname(image_folder)
            )
            if not mask_folder:
                return

            # Demander le dossier de sortie
            output_folder = filedialog.askdirectory(
                title="Sélectionner le dossier de sortie",
                initialdir=os.path.dirname(image_folder)
            )
            if not output_folder:
                return

            # Obtenir la valeur de transparence (0-100 -> 0-1)
            alpha = self._alpha_var.get() / 100.0

            # Créer les overlays
            self._controller.create_overlay(image_folder, mask_folder, output_folder, alpha)
            messagebox.showinfo("Succès", "Overlays créés avec succès!")

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de la création des overlays: {str(e)}")

    def _build_ui(self) -> None:
        """Construit l'interface utilisateur optimisée."""
        # Label du nom de fichier
        self._filename_label = tk.Label(self._root, text="", font=self.FONT_CONFIG)
        self._filename_label.pack(pady=5)

        # Frame pour l'index de l'image
        index_frame = tk.Frame(self._root)
        index_frame.pack(fill=tk.X, padx=5, pady=2)
        
        # Label pour l'index
        index_label = tk.Label(index_frame, text="Index de l'image:", font=("Arial", 10))
        index_label.pack(side=tk.LEFT, padx=5)
        
        # Spinbox pour l'index
        self._image_index_var = tk.StringVar(value="0")
        self._image_index_spinbox = tk.Spinbox(
            index_frame,
            from_=0,
            to=0,
            textvariable=self._image_index_var,
            width=5,
            command=self._on_image_index_change
        )
        self._image_index_spinbox.pack(side=tk.LEFT, padx=5)
        
        # Label pour le nombre total d'images
        self._total_images_label = tk.Label(index_frame, text="/ 0", font=("Arial", 10))
        self._total_images_label.pack(side=tk.LEFT, padx=5)

        # Label pour le type de label actuel
        self._current_label_display = tk.Label(
            self._root,
            text="Label actuel: frontwall",
            font=("Arial", 10, "bold"),
            fg=self.LABEL_COLORS["frontwall"]
        )
        self._current_label_display.pack(pady=2)

        # Frame principal pour organiser les contrôles en 2 colonnes
        main_controls_frame = tk.Frame(self._root)
        main_controls_frame.pack(fill=tk.X, padx=5, pady=5)

        # Colonne gauche : Threshold et Transparence
        left_column = tk.Frame(main_controls_frame)
        left_column.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # Frame pour le seuil
        threshold_frame = tk.Frame(left_column)
        threshold_frame.pack(fill=tk.X, pady=2)
        tk.Label(threshold_frame, text="Threshold:", font=("Arial", 10, "bold")).pack(side=tk.LEFT)

        # Slider pour le threshold
        self.threshold_slider = tk.Scale(
            threshold_frame,
            from_=0,
            to=255,
            orient='horizontal',
            length=200,
            variable=self._threshold_var,
            command=self._on_threshold_change
        )
        self.threshold_slider.pack(side=tk.LEFT, padx=5)

        # Ajouter des événements pour la mise à jour en temps réel
        self.threshold_slider.bind('<ButtonRelease-1>', self._on_threshold_slider_release)
        self.threshold_slider.bind('<B1-Motion>', self._on_threshold_slider_motion)

        # Frame pour le champ de saisie et les boutons flèches
        threshold_entry_frame = tk.Frame(threshold_frame)
        threshold_entry_frame.pack(side=tk.LEFT, padx=5)

        # Champ de saisie numérique pour le threshold (lecture seule)
        self.threshold_entry = tk.Entry(
            threshold_entry_frame,
            textvariable=self._threshold_var,
            width=6,
            font=("Arial", 10),
            justify="center",
            state="readonly"
        )
        self.threshold_entry.pack(side=tk.LEFT)

        # Boutons flèches pour le threshold
        threshold_arrows_frame = tk.Frame(threshold_entry_frame)
        threshold_arrows_frame.pack(side=tk.LEFT, padx=2)

        tk.Button(
            threshold_arrows_frame,
            text="▲",
            font=("Arial", 8),
            width=2,
            height=1,
            command=lambda: self._increment_threshold(1)
        ).pack()

        tk.Button(
            threshold_arrows_frame,
            text="▼",
            font=("Arial", 8),
            width=2,
            height=1,
            command=lambda: self._increment_threshold(-1)
        ).pack()

        # Label pour indiquer la plage
        tk.Label(threshold_frame, text="(0-255)", font=("Arial", 8), fg="gray").pack(side=tk.LEFT, padx=2)

        # Frame pour la transparence
        alpha_frame = tk.Frame(left_column)
        alpha_frame.pack(fill=tk.X, pady=2)
        tk.Label(alpha_frame, text="Transparence (%):", font=("Arial", 10, "bold")).pack(side=tk.LEFT)

        # Slider pour la transparence
        self.alpha_slider = tk.Scale(
            alpha_frame,
            from_=0,
            to=100,
            orient='horizontal',
            length=200,
            variable=self._alpha_var,
            command=self._on_alpha_change
        )
        self.alpha_slider.pack(side=tk.LEFT, padx=5)

        # Ajouter des événements pour la mise à jour en temps réel
        self.alpha_slider.bind('<ButtonRelease-1>', self._on_alpha_slider_release)
        self.alpha_slider.bind('<B1-Motion>', self._on_alpha_slider_motion)

        # Frame pour le champ de saisie et les boutons flèches
        alpha_entry_frame = tk.Frame(alpha_frame)
        alpha_entry_frame.pack(side=tk.LEFT, padx=5)

        # Champ de saisie numérique pour la transparence (lecture seule)
        self.alpha_entry = tk.Entry(
            alpha_entry_frame,
            textvariable=self._alpha_var,
            width=6,
            font=("Arial", 10),
            justify="center",
            state="readonly"
        )
        self.alpha_entry.pack(side=tk.LEFT)

        # Boutons flèches pour la transparence
        alpha_arrows_frame = tk.Frame(alpha_entry_frame)
        alpha_arrows_frame.pack(side=tk.LEFT, padx=2)

        tk.Button(
            alpha_arrows_frame,
            text="▲",
            font=("Arial", 8),
            width=2,
            height=1,
            command=lambda: self._increment_alpha(1)
        ).pack()

        tk.Button(
            alpha_arrows_frame,
            text="▼",
            font=("Arial", 8),
            width=2,
            height=1,
            command=lambda: self._increment_alpha(-1)
        ).pack()

        # Label pour indiquer la plage
        tk.Label(alpha_frame, text="(0-100)", font=("Arial", 8), fg="gray").pack(side=tk.LEFT, padx=2)

        # Colonne droite : Options compactes
        right_column = tk.Frame(main_controls_frame)
        right_column.pack(side=tk.RIGHT, padx=20)

        # Type de masque dans la colonne droite
        mask_type_frame = tk.Frame(right_column)
        mask_type_frame.pack(anchor="w", pady=2)
        tk.Label(mask_type_frame, text="Type de masque:", font=("Arial", 9, "bold")).pack(anchor="w")

        mask_radio_frame = tk.Frame(mask_type_frame)
        mask_radio_frame.pack(anchor="w")

        self.mask_standard_radio = tk.Radiobutton(
            mask_radio_frame,
            text="Standard",
            variable=self._mask_type_var,
            value="standard",
            command=self._on_mask_type_change
        )
        self.mask_standard_radio.pack(side=tk.LEFT)

        self.mask_polygon_radio = tk.Radiobutton(
            mask_radio_frame,
            text="Polygonal",
            variable=self._mask_type_var,
            value="polygon",
            command=self._on_mask_type_change
        )
        self.mask_polygon_radio.pack(side=tk.LEFT, padx=5)

        # Mode de dessin dans la colonne droite
        drawing_mode_frame = tk.Frame(right_column)
        drawing_mode_frame.pack(anchor="w", pady=5)
        tk.Label(drawing_mode_frame, text="Mode de dessin:", font=("Arial", 9, "bold")).pack(anchor="w")

        drawing_radio_frame = tk.Frame(drawing_mode_frame)
        drawing_radio_frame.pack(anchor="w")

        self.polygon_radio = tk.Radiobutton(
            drawing_radio_frame,
            text="Polygone",
            variable=self._drawing_mode_var,
            value="polygon",
            command=self._on_drawing_mode_change
        )
        self.polygon_radio.pack(side=tk.LEFT)

        self.rectangle_radio = tk.Radiobutton(
            drawing_radio_frame,
            text="Rectangle",
            variable=self._drawing_mode_var,
            value="rectangle",
            command=self._on_drawing_mode_change
        )
        self.rectangle_radio.pack(side=tk.LEFT, padx=5)

        # Frame pour les informations en bas des contrôles
        info_frame = tk.Frame(self._root)
        info_frame.pack(fill=tk.X, padx=5, pady=2)

        # Informations sur le type de masque et mode de dessin
        self.mask_info_label = tk.Label(
            info_frame,
            text="Standard: polygones directs (pas de profils automatiques)",
            font=("Arial", 8),
            fg="gray"
        )
        self.mask_info_label.pack(side=tk.LEFT)

        self.drawing_info_label = tk.Label(
            info_frame,
            text="Polygone: Clic pour ajouter des points, clic près du premier pour fermer",
            font=("Arial", 8),
            fg="blue"
        )
        self.drawing_info_label.pack(side=tk.RIGHT)

        # Frame pour la navigation et actions
        nav = tk.Frame(self._root)
        nav.pack()
        tk.Button(nav, text="Open Folder", command=self._open_folder).pack(side=tk.LEFT, padx=3)
        tk.Button(nav, text="Previous", command=self._controller.previous_image).pack(side=tk.LEFT)
        tk.Button(nav, text="Next", command=self._controller.next_image).pack(side=tk.LEFT)

        # Bouton pour valider les polygones temporaires
        self._commit_button = tk.Button(
            nav,
            text="Ajouter au label",
            command=self._commit_temporary_polygons,
            bg="#4CAF50",
            fg="white",
            font=("Arial", 10, "bold"),
            state="disabled"  # Désactivé par défaut
        )
        self._commit_button.pack(side=tk.LEFT, padx=5)

        # Bouton pour annuler les polygones temporaires
        self._cancel_button = tk.Button(
            nav,
            text="Annuler sélection",
            command=self._cancel_temporary_polygons,
            bg="#f44336",
            fg="white",
            font=("Arial", 10, "bold"),
            state="disabled"  # Désactivé par défaut
        )
        self._cancel_button.pack(side=tk.LEFT, padx=2)

        tk.Button(nav, text="Export Masks", command=self._export_masks).pack(side=tk.LEFT, padx=3)

        # Canvas pour l'affichage de l'image
        self._canvas = tk.Canvas(self._root, bg="white")
        self._canvas.pack(fill=tk.BOTH, expand=True)

        # Ajouter le contrôle pour la distance verticale
        vertical_gap_frame = ttk.LabelFrame(main_controls_frame, text="Distance verticale max (pixels)")
        vertical_gap_frame.pack(fill=tk.X, padx=5, pady=5)

        # Frame pour organiser le slider, l'entry et les boutons flèches
        vertical_gap_controls = tk.Frame(vertical_gap_frame)
        vertical_gap_controls.pack(fill=tk.X, padx=5, pady=5)

        # Slider pour la distance verticale
        self.vertical_gap_slider = tk.Scale(
            vertical_gap_controls,
            from_=0,
            to=50,
            orient='horizontal',
            length=150,
            variable=self._vertical_gap_var,
            command=self._on_vertical_gap_change
        )
        self.vertical_gap_slider.pack(side=tk.LEFT, padx=(0, 5))

        # Frame pour l'entry et les boutons flèches
        vertical_gap_entry_frame = tk.Frame(vertical_gap_controls)
        vertical_gap_entry_frame.pack(side=tk.LEFT, padx=5)

        # Entry pour la distance verticale (lecture seule)
        self.vertical_gap_entry = tk.Entry(
            vertical_gap_entry_frame,
            textvariable=self._vertical_gap_var,
            width=6,
            font=("Arial", 10),
            justify="center",
            state="readonly"
        )
        self.vertical_gap_entry.pack(side=tk.LEFT)

        # Boutons flèches pour la distance verticale
        vertical_gap_arrows_frame = tk.Frame(vertical_gap_entry_frame)
        vertical_gap_arrows_frame.pack(side=tk.LEFT, padx=2)

        tk.Button(
            vertical_gap_arrows_frame,
            text="▲",
            font=("Arial", 8),
            width=2,
            height=1,
            command=lambda: self._increment_vertical_gap(1)
        ).pack()

        tk.Button(
            vertical_gap_arrows_frame,
            text="▼",
            font=("Arial", 8),
            width=2,
            height=1,
            command=lambda: self._increment_vertical_gap(-1)
        ).pack()

        # Label pour indiquer la plage
        tk.Label(vertical_gap_controls, text="(0-50+)", font=("Arial", 8), fg="gray").pack(side=tk.LEFT, padx=2)

    def _on_image_index_change(self) -> None:
        """Gère le changement d'index de l'image."""
        try:
            new_index = int(self._image_index_var.get())
            if new_index >= 0 and new_index < len(self._controller._model.image_list):
                self._controller._model.current_index = new_index
                self._controller.load_image()
        except ValueError:
            # En cas d'erreur de conversion, on remet l'ancienne valeur
            self._image_index_var.set(str(self._controller._model.current_index))

    def update_filename(self, filename: str) -> None:
        """Met à jour le nom du fichier affiché."""
        self._filename_label.config(text=filename)
        
        # Mettre à jour l'index et le nombre total d'images
        if self._controller._model.image_list:
            self._image_index_spinbox.config(to=len(self._controller._model.image_list) - 1)
            self._image_index_var.set(str(self._controller._model.current_index))
            self._total_images_label.config(text=f"/ {len(self._controller._model.image_list)}")

    def update_current_label_display(self) -> None:
        """Met à jour l'affichage du label actuel et synchronise le threshold."""
        current_label = self._controller.get_current_label()
        self._current_label_display.config(
            text=f"Label actuel: {current_label}",
            fg=self.LABEL_COLORS[current_label]
        )

        # Synchroniser le slider de threshold avec la valeur stockée dans le modèle
        current_threshold = self._controller._model.threshold
        self._threshold_var.set(current_threshold)
        self.threshold_slider.set(current_threshold)

        self._logger.debug(f"Threshold synchronisé pour {current_label}: {current_threshold}")
        # Note: Plus besoin de charger des paramètres par label car ils sont liés aux polygones

    def _update_info_labels(self) -> None:
        """Met à jour les labels d'information selon les paramètres actuels."""
        # Mettre à jour le label d'information du type de masque
        mask_type = self._mask_type_var.get()
        if mask_type == "standard":
            info_text = "Standard: polygones directs (pas de profils automatiques)"
        else:
            info_text = "Polygonal: profils automatiques pour TOUS les types (frontwall, backwall, flaw, indication)"
        self.mask_info_label.config(text=info_text)

        # Mettre à jour le label d'information du mode de dessin
        mode = self._drawing_mode_var.get()
        if mode == "polygon":
            info_text = "Polygone: Clic pour ajouter des points, clic près du premier pour fermer"
        else:
            info_text = "Rectangle: Premier clic = coin, deuxième clic = coin opposé"
        self.drawing_info_label.config(text=info_text)

    def _bind_events(self) -> None:
        """Configure les événements de la souris et du clavier."""
        self._canvas.bind("<Button-1>", self._add_point)
        self._canvas.bind("<B1-Motion>", self._free_draw)
        self._canvas.bind("<Motion>", self._on_mouse_motion)  # Pour la prévisualisation du rectangle
        self._canvas.bind("<ButtonPress-2>", self._start_pan)
        self._canvas.bind("<B2-Motion>", self._do_pan)
        self._canvas.bind("<MouseWheel>", self._zoom_image)
        self._root.bind("<space>", self._switch_label)
        self._root.bind("<Control-z>", self._undo_polygon)
        self._root.bind("<Escape>", self._cancel_polygon)
        
        # Touche espace pour changer de label
        self._root.bind('<space>', self._on_space_pressed)

    def update_image_display(self, image_bgr: np.ndarray, is_original: bool = True) -> None:
        """
        Met à jour l'affichage de l'image.

        Args:
            image_bgr: Image au format BGR
            is_original: True si c'est l'image originale, False si c'est un overlay
        """
        if is_original:
            # Stocker l'image originale
            self._original_image = image_bgr.copy()
            self._cached_mask = None  # Invalider le cache du masque
            self._cached_mask_key = None  # Invalider la clé du cache

        self._image = image_bgr
        image_rgb = cv2.cvtColor(image_bgr, cv2.COLOR_BGR2RGB)
        zoomed = cv2.resize(
            image_rgb,
            (int(image_rgb.shape[1] * self._zoom), int(image_rgb.shape[0] * self._zoom))
        )
        img = Image.fromarray(zoomed)
        self._tk_image = ImageTk.PhotoImage(img)
        self._canvas.delete("all")
        self._canvas_img = self._canvas.create_image(
            self._offset[0],
            self._offset[1],
            anchor="nw",
            image=self._tk_image
        )
        self._canvas.config(scrollregion=self._canvas.bbox("all"))
        self._draw_polygons()

    def _image_to_canvas_coords(self, x: int, y: int) -> Tuple[int, int]:
        """
        Convertit les coordonnées de l'image en coordonnées du canvas.
        
        Args:
            x: Coordonnée x de l'image
            y: Coordonnée y de l'image
            
        Returns:
            Tuple[int, int]: Coordonnées dans le canvas
        """
        return int(x * self._zoom + self._offset[0]), int(y * self._zoom + self._offset[1])

    def _canvas_to_image_coords(self, x: int, y: int) -> Tuple[int, int]:
        """
        Convertit les coordonnées du canvas en coordonnées de l'image.
        
        Args:
            x: Coordonnée x du canvas
            y: Coordonnée y du canvas
            
        Returns:
            Tuple[int, int]: Coordonnées dans l'image
        """
        return int((x - self._offset[0]) / self._zoom), int((y - self._offset[1]) / self._zoom)

    def _open_folder(self) -> None:
        """Ouvre une boîte de dialogue pour sélectionner un dossier d'images."""
        path = filedialog.askdirectory()
        if path:
            self._controller.open_folder(path)

    def _export_masks(self) -> None:
        """Exporte les masques avec les paramètres individuels de chaque polygone."""
        # Les paramètres sont maintenant stockés avec chaque polygone
        # Plus besoin de passer des paramètres par label
        self._controller.export_individual_masks({})

    def _load_json_annotations(self) -> None:
        """Charge les annotations depuis un fichier JSON."""
        from tkinter import filedialog, messagebox

        # Obtenir le dossier de l'image courante
        current_image_path = self._controller._model.image_list[self._controller._model.current_index]
        initial_dir = os.path.dirname(current_image_path)

        # Sélectionner le fichier JSON
        json_path = filedialog.askopenfilename(
            title="Charger annotations JSON",
            filetypes=[
                ("Fichiers JSON", "*.json"),
                ("Tous les fichiers", "*.*")
            ],
            initialdir=initial_dir
        )

        if not json_path:
            return

        try:
            # Charger les annotations
            success = self._controller.load_annotations_from_json(json_path)

            if success:
                # Redessiner les polygones
                self._draw_polygons()
                self._update_mask_display()

                messagebox.showinfo(
                    "Chargement réussi",
                    f"Annotations chargées depuis:\n{os.path.basename(json_path)}\n\n"
                    "Les polygones ont été mis à jour."
                )

                self._logger.info(f"Annotations JSON chargées: {json_path}")
            else:
                messagebox.showerror(
                    "Erreur de chargement",
                    f"Impossible de charger les annotations depuis:\n{os.path.basename(json_path)}"
                )

        except Exception as e:
            messagebox.showerror(
                "Erreur",
                f"Erreur lors du chargement:\n{str(e)}"
            )
            self._logger.error(f"Erreur chargement JSON: {str(e)}")

    def _add_point(self, event) -> None:
        """
        Ajoute un point au polygone en cours selon le mode de dessin.

        Args:
            event: Événement de la souris
        """
        x, y = self._canvas_to_image_coords(
            self._canvas.canvasx(event.x),
            self._canvas.canvasy(event.y)
        )

        mode = self._drawing_mode_var.get()

        if mode == "rectangle":
            self._handle_rectangle_click(x, y)
        else:  # mode polygon
            self._handle_polygon_click(x, y)

        self._draw_polygons()

    def _handle_rectangle_click(self, x, y):
        """Gère les clics en mode rectangle."""
        if self._rectangle_start is None:
            # Premier clic : définir le coin de départ
            self._rectangle_start = (x, y)
            self._logger.info(f"Rectangle démarré au point ({x}, {y})")
        else:
            # Deuxième clic : créer le rectangle
            start_x, start_y = self._rectangle_start

            # Créer un rectangle avec les 4 coins
            rectangle_polygon = [
                (start_x, start_y),      # Coin de départ
                (x, start_y),            # Coin supérieur droit
                (x, y),                  # Coin d'arrivée
                (start_x, y),            # Coin inférieur gauche
                (start_x, start_y)       # Fermer le rectangle
            ]

            # Obtenir l'image de base et le threshold actuel
            if self._original_image is None:
                return

            # Obtenir les paramètres actuels de l'interface
            threshold = self._threshold_var.get()
            vertical_gap = self._vertical_gap_var.get()

            # Créer un masque temporaire pour le rectangle
            h, w = self._original_image.shape[:2]
            gray = cv2.cvtColor(self._original_image, cv2.COLOR_BGR2GRAY)
            binary_mask = (gray < threshold).astype(np.uint8) * 255

            # Créer le masque du rectangle
            points = np.array(rectangle_polygon[:-1], np.int32)
            temp_mask = np.zeros((h, w), dtype=np.uint8)
            cv2.fillPoly(temp_mask, [points], 1)

            # Appliquer le threshold
            temp_mask &= binary_mask

            # Trouver les contours des tâches dans le rectangle
            contours, _ = cv2.findContours(temp_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)

            # Fusionner les contours qui sont proches verticalement
            merged_contours = self._merge_vertical_polygons(contours, vertical_gap)

            # Pour chaque contour trouvé, créer un polygone
            for contour in merged_contours:
                # Simplifier le contour pour réduire le nombre de points
                epsilon = 0.001 * cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, epsilon, True)
                
                # Convertir les points en liste de tuples
                polygon_points = [(int(p[0][0]), int(p[0][1])) for p in approx]
                
                # Fermer le polygone
                if len(polygon_points) >= 3:
                    polygon_points.append(polygon_points[0])
                    
                    # Transformer le polygone en contour pixelisé
                    pixelized_polygon = self._transform_to_pixelized_contour(polygon_points)

                    # Créer les paramètres actuels
                    current_parameters = {
                        'threshold': self._threshold_var.get(),
                        'alpha': self._alpha_var.get(),
                        'mask_type': self._mask_type_var.get(),
                        'smooth_contours': self._smooth_contours_var.get(),
                        'drawing_mode': self._drawing_mode_var.get(),
                        'vertical_gap': self._vertical_gap_var.get()
                    }

                    # Ajouter le polygone temporaire avec ses paramètres
                    self._controller.add_temporary_polygon(pixelized_polygon, current_parameters)

            # Réinitialiser pour le prochain rectangle
            self._rectangle_start = None
            self._rectangle_preview = None

            # Mettre à jour l'état des boutons et l'affichage
            self._update_temporary_buttons_state()
            self._update_mask_display()

            self._logger.info(f"Rectangle traité de ({start_x}, {start_y}) à ({x}, {y})")

    def _handle_polygon_click(self, x, y):
        """Gère les clics en mode polygone."""
        if self._current_polygon and np.linalg.norm(
            np.array((x, y)) - np.array(self._current_polygon[0])
        ) < self.POINT_RADIUS:
            # Fermer le polygone
            self._current_polygon.append(self._current_polygon[0])

            # Obtenir l'image de base et le threshold actuel
            if self._original_image is None:
                return

            # Obtenir les paramètres actuels de l'interface
            threshold = self._threshold_var.get()
            vertical_gap = self._vertical_gap_var.get()

            # Créer un masque temporaire pour le polygone
            h, w = self._original_image.shape[:2]
            gray = cv2.cvtColor(self._original_image, cv2.COLOR_BGR2GRAY)
            binary_mask = (gray < threshold).astype(np.uint8) * 255

            # Créer le masque du polygone
            points = np.array(self._current_polygon[:-1], np.int32)
            temp_mask = np.zeros((h, w), dtype=np.uint8)
            cv2.fillPoly(temp_mask, [points], 1)

            # Appliquer le threshold
            temp_mask &= binary_mask

            # Trouver les contours des tâches dans le polygone
            contours, _ = cv2.findContours(temp_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)

            # Fusionner les contours qui sont proches verticalement
            merged_contours = self._merge_vertical_polygons(contours, vertical_gap)

            # Pour chaque contour trouvé, créer un polygone
            for contour in merged_contours:
                # Simplifier le contour pour réduire le nombre de points
                epsilon = 0.001 * cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, epsilon, True)
                
                # Convertir les points en liste de tuples
                polygon_points = [(int(p[0][0]), int(p[0][1])) for p in approx]
                
                # Fermer le polygone
                if len(polygon_points) >= 3:
                    polygon_points.append(polygon_points[0])
                    
                    # Transformer le polygone en contour pixelisé
                    pixelized_polygon = self._transform_to_pixelized_contour(polygon_points)

                    # Créer les paramètres actuels
                    current_parameters = {
                        'threshold': self._threshold_var.get(),
                        'alpha': self._alpha_var.get(),
                        'mask_type': self._mask_type_var.get(),
                        'smooth_contours': self._smooth_contours_var.get(),
                        'drawing_mode': self._drawing_mode_var.get(),
                        'vertical_gap': self._vertical_gap_var.get()
                    }

                    # Ajouter le polygone temporaire avec ses paramètres
                    self._controller.add_temporary_polygon(pixelized_polygon, current_parameters)

            # Réinitialiser le polygone en cours
            self._current_polygon = []

            # Mettre à jour l'état des boutons et l'affichage
            self._update_temporary_buttons_state()
            self._update_mask_display()
        else:
            # Ajouter un point au polygone en cours
            self._current_polygon.append((x, y))

    def _transform_to_pixelized_contour(self, original_polygon):
        """
        Transforme un polygone original en contour pixelisé basé sur le masque généré.

        Args:
            original_polygon: Liste des points du polygone original [(x, y), ...]

        Returns:
            Liste des points du contour pixelisé [(x, y), ...]
        """
        try:
            if len(original_polygon) < 3:
                return original_polygon

            # Obtenir les paramètres actuels de l'interface
            current_label = self._controller.get_current_label()
            threshold = self._threshold_var.get()
            mask_type = self._mask_type_var.get()
            smooth_enabled = self._smooth_contours_var.get()

            # Obtenir l'image de base
            if self._original_image is None:
                return original_polygon

            base_image = self._original_image
            h, w = base_image.shape[:2]

            # Créer un masque temporaire pour ce polygone uniquement
            gray = cv2.cvtColor(base_image, cv2.COLOR_BGR2GRAY)
            binary_mask = (gray < threshold).astype(np.uint8) * 255

            # Créer le masque du polygone
            points = np.array(original_polygon[:-1], np.int32)  # Enlever le dernier point dupliqué
            temp_mask = np.zeros((h, w), dtype=np.uint8)
            cv2.fillPoly(temp_mask, [points], 1)

            # Appliquer le threshold
            temp_mask &= binary_mask

            # Appliquer le type de masque
            if mask_type == "polygon":
                # Générer le profil pour frontwall/backwall
                label_value = {"frontwall": 1, "backwall": 2, "flaw": 3, "indication": 4}[current_label]
                temp_mask = self._generate_profile_mask(temp_mask, label_value)

            # Appliquer le lissage si activé
            if smooth_enabled:
                # Convertir le rayon en entier pour le kernel
                kernel_size = max(1, int(0.5 * 2))  # Rayon fixe 0.5
                kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (kernel_size, kernel_size))
                # Appliquer une fermeture morphologique (dilatation suivie d'une érosion)
                temp_mask = cv2.morphologyEx(temp_mask, cv2.MORPH_CLOSE, kernel)

            # Extraire le contour DIRECTEMENT des pixels du masque (sans cv2.findContours)
            # pour éviter tout arrondi et avoir un contour pixel par pixel exact
            pixelized_points = self._extract_pixel_perfect_contour(temp_mask)

            if not pixelized_points:
                # Si aucun contour trouvé, retourner le polygone original
                self._logger.warning(f"Aucun pixel trouvé pour le polygone {current_label}")
                return original_polygon

            # S'assurer que le polygone est fermé
            if len(pixelized_points) >= 3 and pixelized_points[-1] != pixelized_points[0]:
                pixelized_points.append(pixelized_points[0])

            self._logger.info(f"Polygone {current_label} transformé: {len(original_polygon)} -> {len(pixelized_points)} points")

            return pixelized_points

        except Exception as e:
            self._logger.error(f"Erreur lors de la transformation en contour pixelisé: {str(e)}")
            return original_polygon

    def _extract_pixel_perfect_contour(self, mask):
        """
        Extrait le contour directement des pixels du masque, sans aucun arrondi.
        Utilise cv2.findContours avec CHAIN_APPROX_NONE mais sur une version agrandie
        du masque pour éviter l'arrondi et avoir des contours parfaitement carrés.

        Args:
            mask: Masque binaire (0 ou 1)

        Returns:
            Liste des points du contour [(x, y), ...]
        """
        try:
            if not np.any(mask):
                return []

            # Agrandir le masque par un facteur pour éviter l'arrondi de cv2.findContours
            scale_factor = 10
            h, w = mask.shape
            enlarged_mask = np.zeros((h * scale_factor, w * scale_factor), dtype=np.uint8)

            # Pour chaque pixel du masque original, créer un carré dans le masque agrandi
            y_coords, x_coords = np.where(mask > 0)

            for x, y in zip(x_coords, y_coords):
                # Créer un carré parfait de scale_factor x scale_factor
                x_start = x * scale_factor
                x_end = x_start + scale_factor
                y_start = y * scale_factor
                y_end = y_start + scale_factor

                enlarged_mask[y_start:y_end, x_start:x_end] = 255

            # Extraire les contours du masque agrandi
            contours, _ = cv2.findContours(enlarged_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)

            if not contours:
                self._logger.warning(f"Aucun contour trouvé dans le masque agrandi")
                return []

            # Prendre le plus grand contour
            largest_contour = max(contours, key=cv2.contourArea)

            # Convertir les points du contour agrandi vers les coordonnées originales
            original_points = []
            for point in largest_contour:
                x_enlarged = int(point[0][0])
                y_enlarged = int(point[0][1])

                # Convertir vers les coordonnées originales
                x_original = x_enlarged / scale_factor
                y_original = y_enlarged / scale_factor

                original_points.append((x_original, y_original))

            # Simplifier pour garder seulement les points aux coins des pixels
            simplified_points = self._simplify_to_pixel_corners(original_points)

            # S'assurer que le polygone est fermé
            if len(simplified_points) >= 3 and simplified_points[-1] != simplified_points[0]:
                simplified_points.append(simplified_points[0])

            self._logger.info(f"Contour pixel parfait: {len(original_points)} points agrandis -> {len(simplified_points)} points simplifiés")

            return simplified_points

        except Exception as e:
            self._logger.error(f"Erreur lors de l'extraction du contour pixel parfait: {str(e)}")
            return []

    def _simplify_to_pixel_corners(self, points):
        """
        Simplifie les points pour garder seulement les coins des pixels.

        Args:
            points: Liste des points du contour [(x, y), ...]

        Returns:
            Liste des points simplifiés aux coins des pixels
        """
        if len(points) < 3:
            return points

        # Arrondir tous les points aux coins des pixels (coordonnées entières)
        rounded_points = []
        for x, y in points:
            # Arrondir aux coins des pixels
            rounded_x = round(x)
            rounded_y = round(y)
            rounded_points.append((rounded_x, rounded_y))

        # Supprimer les points dupliqués consécutifs
        simplified = []
        for i, point in enumerate(rounded_points):
            if i == 0 or point != rounded_points[i-1]:
                simplified.append(point)

        # Supprimer les points colinéaires pour garder seulement les coins
        if len(simplified) < 3:
            return simplified

        final_points = []
        for i in range(len(simplified)):
            if len(final_points) < 2:
                final_points.append(simplified[i])
            else:
                # Vérifier si les 3 derniers points sont colinéaires
                p1 = final_points[-2]
                p2 = final_points[-1]
                p3 = simplified[i]

                # Calculer les vecteurs
                v1 = (p2[0] - p1[0], p2[1] - p1[1])
                v2 = (p3[0] - p2[0], p3[1] - p2[1])

                # Produit vectoriel pour détecter la colinéarité
                cross_product = v1[0] * v2[1] - v1[1] * v2[0]

                if abs(cross_product) < 0.1:  # Points colinéaires
                    # Remplacer le point précédent par le nouveau
                    final_points[-1] = simplified[i]
                else:
                    # Ajouter le nouveau point (changement de direction)
                    final_points.append(simplified[i])

        return final_points

    def _trace_pixel_contour(self, border_pixels, mask_pixels):
        """
        Trace le contour en suivant les bords des pixels.

        Args:
            border_pixels: Liste des pixels de bord
            mask_pixels: Ensemble de tous les pixels du masque

        Returns:
            Liste ordonnée des points du contour
        """
        if not border_pixels:
            return []

        # Créer une grille pour marquer les segments de contour
        min_x = min(x for x, y in border_pixels)
        max_x = max(x for x, y in border_pixels)
        min_y = min(y for x, y in border_pixels)
        max_y = max(y for x, y in border_pixels)

        # Collecter tous les segments de bord
        edge_segments = []

        for x, y in border_pixels:
            # Pour chaque pixel de bord, ajouter ses segments externes
            # Vérifier chaque côté du pixel

            # Côté gauche (x, y) → (x, y+1)
            if (x-1, y) not in mask_pixels:
                edge_segments.append(((x, y), (x, y+1)))

            # Côté droit (x+1, y) → (x+1, y+1)
            if (x+1, y) not in mask_pixels:
                edge_segments.append(((x+1, y), (x+1, y+1)))

            # Côté haut (x, y) → (x+1, y)
            if (x, y-1) not in mask_pixels:
                edge_segments.append(((x, y), (x+1, y)))

            # Côté bas (x, y+1) → (x+1, y+1)
            if (x, y+1) not in mask_pixels:
                edge_segments.append(((x, y+1), (x+1, y+1)))

        if not edge_segments:
            return []

        # Connecter les segments pour former un contour continu
        contour_points = self._connect_edge_segments(edge_segments)

        return contour_points

    def _connect_edge_segments(self, edge_segments):
        """
        Connecte les segments de bord pour former un contour continu.

        Args:
            edge_segments: Liste des segments [(point1, point2), ...]

        Returns:
            Liste ordonnée des points du contour
        """
        if not edge_segments:
            return []

        # Créer un dictionnaire des connexions
        connections = {}
        for start, end in edge_segments:
            if start not in connections:
                connections[start] = []
            if end not in connections:
                connections[end] = []
            connections[start].append(end)
            connections[end].append(start)

        # Commencer par le premier point
        start_point = edge_segments[0][0]
        contour = [start_point]
        current = start_point
        visited = {start_point}

        # Suivre le contour
        while True:
            # Trouver le prochain point non visité
            next_points = [p for p in connections.get(current, []) if p not in visited]

            if not next_points:
                break

            # Prendre le premier point disponible
            next_point = next_points[0]
            contour.append(next_point)
            visited.add(next_point)
            current = next_point

        # Fermer le contour si nécessaire
        if len(contour) > 2 and contour[-1] != contour[0]:
            contour.append(contour[0])

        return contour

    def _on_threshold_change(self, value):
        """Gère le changement de valeur du seuil pour le label actuel en temps réel."""
        try:
            # Mettre à jour la variable
            self._threshold_var.set(int(value))
            # Sauvegarder pour le label actuel
            # Note: Paramètres maintenant liés aux polygones individuels
            # Mise à jour immédiate pour le temps réel
            self._schedule_mask_update(immediate=True)
            current_label = self._controller.get_current_label()
            self._logger.debug(f"Threshold changé pour {current_label}: {value}")
        except Exception as e:
            self._logger.error(f"Erreur lors du changement de seuil: {str(e)}")



    def _on_alpha_change(self, value):
        """Gère le changement de valeur de la transparence pour le label actuel en temps réel."""
        try:
            # Mettre à jour la variable
            self._alpha_var.set(float(value))
            # Sauvegarder pour le label actuel
            # Note: Paramètres maintenant liés aux polygones individuels
            # Mise à jour immédiate pour le temps réel
            self._schedule_mask_update(immediate=True)
            current_label = self._controller.get_current_label()
            self._logger.debug(f"Transparence changée pour {current_label}: {value}%")
        except Exception as e:
            self._logger.error(f"Erreur lors du changement de transparence: {str(e)}")



    def _on_threshold_slider_motion(self, event=None):
        """Gère le mouvement du slider de threshold en temps réel."""
        try:
            # Forcer la mise à jour immédiate pendant le mouvement
            value = self.threshold_slider.get()
            self._threshold_var.set(int(value))
            # Note: Paramètres maintenant liés aux polygones individuels
            self._schedule_mask_update(immediate=True)
        except Exception as e:
            self._logger.error(f"Erreur lors du mouvement du slider threshold: {str(e)}")

    def _on_threshold_slider_release(self, event=None):
        """Gère le relâchement du slider de threshold."""
        try:
            # Mise à jour finale
            value = self.threshold_slider.get()
            self._threshold_var.set(int(value))
            # Note: Paramètres maintenant liés aux polygones individuels
            self._schedule_mask_update(immediate=True)
            current_label = self._controller.get_current_label()
            self._logger.debug(f"Threshold finalisé pour {current_label}: {value}")
        except Exception as e:
            self._logger.error(f"Erreur lors du relâchement du slider threshold: {str(e)}")

    def _on_alpha_slider_motion(self, event=None):
        """Gère le mouvement du slider d'alpha en temps réel."""
        try:
            # Forcer la mise à jour immédiate pendant le mouvement
            value = self.alpha_slider.get()
            self._alpha_var.set(float(value))
            # Note: Paramètres maintenant liés aux polygones individuels
            self._schedule_mask_update(immediate=True)
        except Exception as e:
            self._logger.error(f"Erreur lors du mouvement du slider alpha: {str(e)}")

    def _on_alpha_slider_release(self, event=None):
        """Gère le relâchement du slider d'alpha."""
        try:
            # Mise à jour finale
            value = self.alpha_slider.get()
            self._alpha_var.set(float(value))
            # Note: Paramètres maintenant liés aux polygones individuels
            self._schedule_mask_update(immediate=True)
            current_label = self._controller.get_current_label()
            self._logger.debug(f"Alpha finalisé pour {current_label}: {value}")
        except Exception as e:
            self._logger.error(f"Erreur lors du relâchement du slider alpha: {str(e)}")

    def _on_threshold_var_change(self, var_name, index, mode):
        """Callback de trace pour la variable threshold - mise à jour en temps réel."""
        try:
            # Cette fonction est appelée chaque fois que _threshold_var change
            # Mise à jour immédiate pour les polygones temporaires
            self._schedule_mask_update(immediate=True)
            value = self._threshold_var.get()
            self._logger.debug(f"Threshold changé: {value}")
        except Exception as e:
            self._logger.error(f"Erreur lors de la trace threshold: {str(e)}")

    def _on_alpha_var_change(self, var_name, index, mode):
        """Callback de trace pour la variable alpha - mise à jour en temps réel."""
        try:
            # Cette fonction est appelée chaque fois que _alpha_var change
            # Mise à jour immédiate pour les polygones temporaires
            self._schedule_mask_update(immediate=True)
            value = self._alpha_var.get()
            self._logger.debug(f"Alpha changé: {value}")
        except Exception as e:
            self._logger.error(f"Erreur lors de la trace alpha: {str(e)}")

    def _on_mask_type_change(self):
        """Gère le changement de type de masque pour le label actuel."""
        try:
            mask_type = self._mask_type_var.get()
            # Sauvegarder pour le label actuel
            # Note: Paramètres maintenant liés aux polygones individuels
            # Mettre à jour le label d'information
            self._update_info_labels()
            # Mise à jour immédiate (le cache sera invalidé automatiquement)
            self._schedule_mask_update(immediate=True)
            current_label = self._controller.get_current_label()
            self._logger.debug(f"Type de masque changé pour {current_label}: {mask_type}")
        except Exception as e:
            self._logger.error(f"Erreur lors du changement de type de masque: {str(e)}")

    def _on_smooth_change(self):
        """Gère le changement d'activation de l'arrondissement pour le label actuel."""
        try:
            smooth_enabled = self._smooth_contours_var.get()
            # Sauvegarder pour le label actuel
            # Note: Paramètres maintenant liés aux polygones individuels
            # Mise à jour immédiate
            self._schedule_mask_update(immediate=True)
            current_label = self._controller.get_current_label()
            self._logger.debug(f"Arrondissement (0.5) pour {current_label}: {'activé' if smooth_enabled else 'désactivé'}")
        except Exception as e:
            self._logger.error(f"Erreur lors du changement d'arrondissement: {str(e)}")

    def _on_drawing_mode_change(self):
        """Gère le changement de mode de dessin pour le label actuel."""
        try:
            mode = self._drawing_mode_var.get()
            # Sauvegarder pour le label actuel
            # Note: Paramètres maintenant liés aux polygones individuels

            # Annuler le polygone en cours lors du changement de mode
            self._current_polygon = []
            self._rectangle_start = None
            self._rectangle_preview = None

            # Mettre à jour le label d'information
            self._update_info_labels()

            # Redessiner pour effacer les prévisualisations
            self._draw_polygons()

            current_label = self._controller.get_current_label()
            self._logger.info(f"Mode de dessin changé pour {current_label}: {mode}")
        except Exception as e:
            self._logger.error(f"Erreur lors du changement de mode de dessin: {str(e)}")

    def _increment_threshold(self, increment: int):
        """
        Incrémente/décrémente la valeur du threshold.

        Args:
            increment: Valeur à ajouter (positive ou négative)
        """
        try:
            current_value = self._threshold_var.get()
            new_value = current_value + increment

            # Valider les limites
            if new_value < 0:
                new_value = 0
            elif new_value > 255:
                new_value = 255

            # Mettre à jour la variable ET le modèle
            self._threshold_var.set(new_value)
            self._controller._model.threshold = new_value

            # Mise à jour immédiate
            self._schedule_mask_update(immediate=True)
            current_label = self._controller.get_current_label()

            # Message de confirmation
            direction = "augmenté" if increment > 0 else "diminué"
            self._logger.info(f"Threshold {direction} pour {current_label}: {new_value}")

            # Afficher brièvement le changement dans le titre de la fenêtre
            old_title = self._root.title()
            self._root.title(f"Threshold {direction}: {new_value}")
            self._root.after(1500, lambda: self._root.title(old_title))

        except Exception as e:
            self._logger.error(f"Erreur lors de l'incrémentation du threshold: {str(e)}")

    def _increment_alpha(self, increment: int):
        """
        Incrémente/décrémente la valeur de la transparence.

        Args:
            increment: Valeur à ajouter (positive ou négative)
        """
        try:
            current_value = self._alpha_var.get()
            new_value = current_value + increment

            # Valider les limites
            if new_value < 0:
                new_value = 0
            elif new_value > 100:
                new_value = 100

            # Mettre à jour la variable
            self._alpha_var.set(new_value)

            # Mise à jour immédiate
            self._schedule_mask_update(immediate=True)
            current_label = self._controller.get_current_label()

            # Message de confirmation
            direction = "augmentée" if increment > 0 else "diminuée"
            self._logger.info(f"Transparence {direction} pour {current_label}: {new_value}%")

            # Afficher brièvement le changement dans le titre de la fenêtre
            old_title = self._root.title()
            self._root.title(f"Transparence {direction}: {new_value}%")
            self._root.after(1500, lambda: self._root.title(old_title))

        except Exception as e:
            self._logger.error(f"Erreur lors de l'incrémentation de la transparence: {str(e)}")

    def _increment_vertical_gap(self, increment: int):
        """
        Incrémente/décrémente la valeur de la distance verticale.

        Args:
            increment: Valeur à ajouter (positive ou négative)
        """
        try:
            current_value = self._vertical_gap_var.get()
            new_value = current_value + increment

            # Valider les limites (minimum 0, maximum du slider)
            if new_value < 0:
                new_value = 0
            elif new_value > self.vertical_gap_slider['to']:
                # Si on dépasse le maximum du slider, l'augmenter
                new_max = new_value + 10
                self.vertical_gap_slider.config(to=new_max)

            # Mettre à jour la variable
            self._vertical_gap_var.set(new_value)

            current_label = self._controller.get_current_label()

            # Message de confirmation
            direction = "augmentée" if increment > 0 else "diminuée"
            self._logger.info(f"Distance verticale {direction} pour {current_label}: {new_value} pixels")

            # Afficher brièvement le changement dans le titre de la fenêtre
            old_title = self._root.title()
            self._root.title(f"Distance verticale {direction}: {new_value} pixels")
            self._root.after(1500, lambda: self._root.title(old_title))

        except Exception as e:
            self._logger.error(f"Erreur lors de l'incrémentation de la distance verticale: {str(e)}")

    def _on_vertical_gap_change(self, value):
        """Gère le changement de valeur du slider de distance verticale."""
        try:
            # Convertir la valeur du slider en entier
            int_value = int(float(value))
            self._vertical_gap_var.set(int_value)

            current_label = self._controller.get_current_label()
            self._logger.debug(f"Distance verticale changée via slider pour {current_label}: {int_value} pixels")

        except Exception as e:
            self._logger.error(f"Erreur lors du changement de distance verticale: {str(e)}")

    def _schedule_mask_update(self, immediate=False):
        """
        Programme une mise à jour du masque.

        Args:
            immediate: Si True, met à jour immédiatement (temps réel)
                      Si False, utilise un délai pour éviter trop d'appels
        """
        if immediate:
            # Mise à jour immédiate pour les interactions en temps réel
            self._update_mask_display()
        else:
            # Mise à jour avec délai pour les opérations lourdes
            if not self._update_pending:
                self._update_pending = True
                # Programmer la mise à jour après 50ms (réduit de 100ms)
                self._root.after(50, self._delayed_mask_update)

    def _delayed_mask_update(self):
        """Effectue la mise à jour du masque après le délai."""
        self._update_pending = False
        self._update_mask_display()

    def _update_mask_display(self) -> None:
        """Met à jour l'affichage du masque en superposition avec paramètres individuels par label."""
        if self._original_image is None:
            return

        try:
            # Utiliser l'image originale pour les calculs
            base_image = self._original_image

            # Créer une clé de cache basée sur les polygones et leurs paramètres individuels
            polygons_with_params = self._controller.get_all_polygons_with_parameters()
            temp_polygons = self._controller.get_temporary_polygons()

            # Inclure les polygones définitifs avec leurs paramètres
            cache_key_parts = []
            for label, polygons in polygons_with_params.items():
                for i, poly_data in enumerate(polygons):
                    params = poly_data['parameters']
                    cache_key_parts.append(f"{label}_{i}:{params.get('threshold', 150)}_{params.get('mask_type', 'standard')}")

            # Inclure les polygones temporaires
            for i, temp_poly in enumerate(temp_polygons):
                params = temp_poly['parameters']
                cache_key_parts.append(f"temp_{i}:{params.get('threshold', 150)}_{params.get('mask_type', 'standard')}")

            cache_key = "|".join(cache_key_parts)

            # Calculer le masque seulement si nécessaire (cache)
            if self._cached_mask is None or self._cached_mask_key != cache_key:
                self._logger.info(f"Recalcul du masque avec paramètres individuels")
                self._cached_mask = self._calculate_individual_mask(base_image)
                self._cached_mask_key = cache_key
            else:
                self._logger.debug(f"Utilisation du cache pour masque individuel")

            # Créer l'overlay avec les couleurs appropriées
            # Utiliser l'alpha actuel de l'interface (pas lié au label)
            alpha = self._alpha_var.get() / 100.0
            overlay = self._apply_colored_mask(base_image, self._cached_mask, alpha)

            # Mettre à jour l'affichage sans marquer comme image originale
            self.update_image_display(overlay, is_original=False)

        except Exception as e:
            self._logger.error(f"Erreur lors de la mise à jour du masque: {str(e)}")

    def _calculate_individual_mask(self, image: np.ndarray) -> np.ndarray:
        """
        Calcule le masque coloré avec paramètres individuels pour chaque label.
        Inclut les polygones temporaires et définitifs.

        Args:
            image: Image de base

        Returns:
            np.ndarray: Masque coloré avec paramètres individuels
        """
        h, w = image.shape[:2]
        final_mask = np.zeros((h, w), dtype=np.uint8)  # Masque simple avec valeurs de classe

        # 1. Traiter les polygones définitifs avec leurs paramètres individuels figés
        polygons_with_params = self._controller.get_all_polygons_with_parameters()

        for label, label_value in [("frontwall", 1), ("backwall", 2), ("flaw", 3), ("indication", 4)]:
            polygons = polygons_with_params[label]

            if not polygons:
                continue

            # Traiter chaque polygone avec ses propres paramètres figés
            for polygon_data in polygons:
                points = polygon_data['points']
                parameters = polygon_data['parameters']

                if len(points) < 3:
                    continue

                # Utiliser les paramètres figés de ce polygone spécifique
                threshold = parameters.get('threshold', 150)
                mask_type = parameters.get('mask_type', 'standard')

                # Convertir l'image en niveaux de gris et appliquer le seuil spécifique
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
                binary_mask = (gray < threshold).astype(np.uint8) * 255

                # Créer le masque pour ce polygone spécifique
                poly_mask = np.zeros((h, w), dtype=np.uint8)
                points_array = np.array(points[:-1], np.int32)  # Enlever le dernier point dupliqué
                cv2.fillPoly(poly_mask, [points_array], 1)
                poly_mask &= binary_mask

                # Appliquer le type de masque spécifique à ce polygone
                if mask_type == "polygon":
                    # Générer le profil pour ce polygone spécifique
                    poly_mask = self._generate_profile_mask_with_params(poly_mask, label_value, parameters)
                else:
                    poly_mask[poly_mask > 0] = label_value

                # Fusionner avec le masque final
                final_mask = np.maximum(final_mask, poly_mask)

        # 2. Traiter les polygones temporaires avec leurs paramètres individuels
        temp_polygons = self._controller.get_temporary_polygons()
        for temp_polygon in temp_polygons:
            points = temp_polygon['points']
            parameters = temp_polygon['parameters']

            if len(points) < 3:
                continue

            # Utiliser les paramètres spécifiques de ce polygone temporaire
            threshold = parameters['threshold']
            mask_type = parameters['mask_type']

            # Convertir l'image en niveaux de gris et appliquer le seuil
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            binary_mask = (gray < threshold).astype(np.uint8) * 255

            # Créer le masque pour ce polygone temporaire
            temp_mask = np.zeros((h, w), dtype=np.uint8)
            points_array = np.array(points[:-1], np.int32)  # Enlever le dernier point dupliqué
            cv2.fillPoly(temp_mask, [points_array], 1)
            temp_mask &= binary_mask

            # Appliquer le type de masque
            if mask_type == "polygon":
                # Générer le profil pour ce polygone temporaire
                temp_mask = self._generate_profile_mask(temp_mask, 255)  # Valeur temporaire
            else:
                temp_mask[temp_mask > 0] = 255  # Valeur temporaire pour les polygones gris

            # Fusionner avec le masque final (les temporaires écrasent les définitifs)
            final_mask[temp_mask > 0] = 255  # Valeur spéciale pour les temporaires

        return final_mask

    def _apply_colored_mask(self, base_image: np.ndarray, mask: np.ndarray, alpha: float) -> np.ndarray:
        """
        Applique un masque coloré sur l'image de base.

        Args:
            base_image: Image de base
            mask: Masque avec valeurs de classe (1-4 pour définitifs, 255 pour temporaires)
            alpha: Transparence globale

        Returns:
            Image avec overlay coloré
        """
        if base_image is None or mask is None:
            return base_image

        # Créer l'overlay coloré
        overlay = base_image.copy().astype(np.float32)

        # Couleurs pour les labels définitifs
        from config.constants import MASK_COLORS_BGR
        for class_value, bgr_color in MASK_COLORS_BGR.items():
            class_pixels = (mask == class_value)
            if np.any(class_pixels):
                for c in range(3):
                    overlay[class_pixels, c] = (
                        base_image[class_pixels, c].astype(np.float32) * (1 - alpha) +
                        bgr_color[c] * alpha
                    )

        # Couleur grise pour les polygones temporaires
        temp_pixels = (mask == 255)
        if np.any(temp_pixels):
            gray_color = [128, 128, 128]  # Gris
            for c in range(3):
                overlay[temp_pixels, c] = (
                    base_image[temp_pixels, c].astype(np.float32) * (1 - alpha) +
                    gray_color[c] * alpha
                )

        return overlay.astype(np.uint8)

    def _generate_profile_mask_with_params(self, single_mask: np.ndarray, val: int, parameters: Dict) -> np.ndarray:
        """
        Génère un masque de profil avec les paramètres spécifiques du polygone.

        Args:
            single_mask: Masque binaire initial
            val: Valeur du label à appliquer
            parameters: Paramètres spécifiques de ce polygone

        Returns:
            Masque respectant strictement le contour avec paramètres du polygone
        """
        try:
            mask_type = parameters.get('mask_type', 'standard')

            # Créer un masque temporaire pour la fusion
            fusion_mask = np.zeros_like(single_mask)

            # Trouver les contours du masque binaire
            contours, _ = cv2.findContours(single_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)

            if not contours:
                return single_mask

            # Fusionner tous les contours en un seul
            if len(contours) > 1:
                # Remplir tous les contours dans le masque de fusion
                cv2.drawContours(fusion_mask, contours, -1, 1, -1)
                # Trouver le contour externe de la fusion
                contours, _ = cv2.findContours(fusion_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)

            # Créer le masque final
            profile_mask = np.zeros_like(single_mask)

            # Pour chaque contour (maintenant fusionné)
            for contour in contours:
                # Remplir le contour avec la valeur du label
                cv2.fillPoly(profile_mask, [contour], val)

            # Appliquer la verticalisation seulement si ce polygone est en mode polygon
            if mask_type == "polygon":
                h, w = profile_mask.shape
                # Pour chaque colonne x
                for x in range(w):
                    # Trouver tous les pixels non-nuls dans cette colonne
                    col = profile_mask[:, x]
                    idx = np.where(col == val)[0]
                    if idx.size > 0:
                        # Prendre le plus petit et le plus grand y
                        y_min = idx.min()
                        y_max = idx.max()
                        # Remplir verticalement entre ces points
                        profile_mask[y_min:y_max+1, x] = val

            return profile_mask

        except Exception as e:
            self._logger.error(f"Erreur lors de la génération du masque de profil avec paramètres: {str(e)}")
            return single_mask

    def _calculate_standard_mask_for_label(self, binary_mask: np.ndarray, h: int, w: int, label: str, polygons: list) -> np.ndarray:
        """Calcule le masque standard pour un label spécifique."""
        mask = np.zeros((h, w), dtype=np.uint8)
        label_value = {"frontwall": 1, "backwall": 2, "flaw": 3, "indication": 4}[label]

        # Créer un masque temporaire pour tous les polygones
        temp_mask = np.zeros_like(mask, dtype=np.uint8)
        
        # Remplir tous les polygones dans le masque temporaire
        for pts in polygons:
            if len(pts) >= 3:
                points = np.array(pts, np.int32)
                cv2.fillPoly(temp_mask, [points], 1)
        
        # Appliquer le threshold sur l'ensemble des polygones
        temp_mask &= binary_mask
        
        # Appliquer la valeur du label
        mask[temp_mask > 0] = label_value

        return mask

    def _calculate_polygon_mask_for_label(self, binary_mask: np.ndarray, h: int, w: int, label: str, polygons: list) -> np.ndarray:
        """Calcule le masque polygonal pour un label spécifique."""
        mask = np.zeros((h, w), dtype=np.uint8)
        label_value = {"frontwall": 1, "backwall": 2, "flaw": 3, "indication": 4}[label]

        for pts in polygons:
            if len(pts) >= 3:
                points = np.array(pts, np.int32)
                tmp = np.zeros_like(mask, dtype=np.uint8)
                cv2.fillPoly(tmp, [points], 1)
                # Appliquer le threshold
                tmp &= binary_mask
                # Générer le masque de profil avec verticalisation pour ce label spécifique
                profile_mask = self._generate_profile_mask_for_label(tmp, label_value, label)
                mask = np.maximum(mask, profile_mask)

        return mask

    def _smooth_mask_contours(self, mask: np.ndarray, radius: float) -> np.ndarray:
        """
        Applique un lissage morphologique aux contours du masque.

        Args:
            mask: Masque binaire à lisser
            radius: Rayon du lissage (en pixels)

        Returns:
            np.ndarray: Masque lissé
        """
        # Convertir le rayon en entier pour le kernel
        kernel_size = max(1, int(radius * 2))
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (kernel_size, kernel_size))
        
        # Appliquer une fermeture morphologique (dilatation suivie d'une érosion)
        smoothed = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
        
        return smoothed

    def _calculate_mask(self, image: np.ndarray, threshold: int) -> np.ndarray:
        """
        Calcule le masque coloré à partir de l'image et du seuil.

        Args:
            image: Image de base
            threshold: Seuil de binarisation

        Returns:
            np.ndarray: Masque coloré
        """
        # Convertir l'image en niveaux de gris et appliquer le seuil
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        binary_mask = (gray < threshold).astype(np.uint8) * 255

        # Créer les masques à partir des polygones
        h, w = image.shape[:2]
        mask_type = self._mask_type_var.get()

        if mask_type == "standard":
            final_mask = self._calculate_standard_mask(binary_mask, h, w)
            self._logger.debug(f"Masque standard calculé - pixels non-zéro: {np.sum(final_mask > 0)}")
        else:  # polygon
            final_mask = self._calculate_polygon_mask(binary_mask, h, w)
            self._logger.debug(f"Masque polygonal calculé - pixels non-zéro: {np.sum(final_mask > 0)}")

        # Appliquer l'arrondissement des contours si activé
        if self._smooth_contours_var.get():
            # Convertir le rayon en entier pour le kernel
            kernel_size = max(1, int(0.5 * 2))  # Rayon fixe 0.5
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (kernel_size, kernel_size))
            # Appliquer une fermeture morphologique (dilatation suivie d'une érosion)
            final_mask = cv2.morphologyEx(final_mask, cv2.MORPH_CLOSE, kernel)

        # Créer une image de masque colorée avec fond transparent (RGBA)
        colored_mask = np.zeros((h, w, 4), dtype=np.uint8)  # Fond transparent (alpha = 0)
        # Appliquer les couleurs selon les valeurs du masque
        for label_value, color in self.MASK_COLORS.items():
            colored_mask[final_mask == label_value] = color

        return colored_mask

    def _blend_transparent_mask(self, base_image: np.ndarray, mask_rgba: np.ndarray, global_alpha: float) -> np.ndarray:
        """
        Mélange une image de base (RGB) avec un masque transparent (RGBA).

        Args:
            base_image: Image de base en RGB
            mask_rgba: Masque avec canal alpha en RGBA
            global_alpha: Transparence globale (0.0 = invisible, 1.0 = opaque)

        Returns:
            np.ndarray: Image résultante en RGB
        """
        if global_alpha <= 0:
            # Transparence à 0% : image originale seulement
            return base_image.copy()

        # Extraire les canaux RGB et alpha du masque
        mask_rgb = mask_rgba[:, :, :3]
        mask_alpha = mask_rgba[:, :, 3].astype(np.float32) / 255.0

        # Appliquer la transparence globale au canal alpha
        mask_alpha *= global_alpha

        # Créer l'overlay en mélangeant pixel par pixel
        overlay = base_image.copy().astype(np.float32)

        # Pour chaque pixel, mélanger selon le canal alpha
        for c in range(3):  # RGB channels
            overlay[:, :, c] = (
                base_image[:, :, c].astype(np.float32) * (1.0 - mask_alpha) +
                mask_rgb[:, :, c].astype(np.float32) * mask_alpha
            )

        return overlay.astype(np.uint8)

    def _merge_vertical_polygons(self, contours, vertical_gap):
        """
        Fusionne les polygones qui sont proches verticalement.
        
        Args:
            contours: Liste des contours à fusionner
            vertical_gap: Distance verticale maximale pour la fusion
            
        Returns:
            Liste des contours fusionnés
        """
        if not contours:
            return contours

        # Créer un masque pour tous les contours
        h, w = self._original_image.shape[:2]
        all_contours_mask = np.zeros((h, w), dtype=np.uint8)
        for contour in contours:
            cv2.drawContours(all_contours_mask, [contour], -1, 255, -1)

        # Pour chaque colonne x
        for x in range(w):
            # Trouver tous les pixels non-nuls dans cette colonne
            col = all_contours_mask[:, x]
            idx = np.where(col > 0)[0]
            if idx.size > 0:
                # Trier les indices pour faciliter le traitement
                idx = np.sort(idx)
                
                # Trouver les groupes de pixels qui sont proches verticalement
                groups = []
                current_group = [idx[0]]
                
                for i in range(1, len(idx)):
                    if idx[i] - idx[i-1] <= vertical_gap:
                        # Les pixels sont assez proches, les ajouter au groupe actuel
                        current_group.append(idx[i])
                    else:
                        # Les pixels sont trop éloignés, créer un nouveau groupe
                        groups.append(current_group)
                        current_group = [idx[i]]
                
                # Ajouter le dernier groupe
                if current_group:
                    groups.append(current_group)
                
                # Pour chaque groupe, remplir verticalement entre le min et le max
                for group in groups:
                    y_min = min(group)
                    y_max = max(group)
                    all_contours_mask[y_min:y_max+1, x] = 255

        # Trouver les contours du masque fusionné
        merged_contours, _ = cv2.findContours(all_contours_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)

        return merged_contours

    def _calculate_polygon_mask(self, binary_mask: np.ndarray, h: int, w: int) -> np.ndarray:
        """
        Calcule le masque polygonal avec paramètres individuels par label.
        DEPRECATED: Cette fonction est conservée pour compatibilité mais utilise les paramètres globaux.
        Utilisez _calculate_individual_mask pour les paramètres par label.

        Args:
            binary_mask: Masque binaire basé sur le threshold
            h: Hauteur de l'image
            w: Largeur de l'image

        Returns:
            np.ndarray: Masque polygonal
        """
        mask_poly = np.zeros((h, w), dtype=np.uint8)
        polygons = self._controller.get_all_polygons()

        # Utiliser les paramètres actuels de l'interface (comportement legacy)
        current_mask_type = self._mask_type_var.get()

        for label, val in {'frontwall': 1, 'backwall': 2, 'flaw': 3, 'indication': 4}.items():
            for pts in polygons[label]:
                if len(pts) >= 3:
                    points = np.array(pts, np.int32)
                    tmp = np.zeros_like(mask_poly, dtype=np.uint8)
                    cv2.fillPoly(tmp, [points], 1)
                    # Appliquer le threshold
                    tmp &= binary_mask

                    # Générer le masque selon le type du label actuel (legacy)
                    if current_mask_type == "polygon":
                        profile_mask = self._generate_profile_mask(tmp, val)
                    else:
                        # Mode standard : pas de profil
                        profile_mask = tmp.copy()
                        profile_mask[profile_mask > 0] = val

                    mask_poly = np.maximum(mask_poly, profile_mask)

        return mask_poly

    def _generate_profile_mask(self, single_mask, val):
        """
        Génère un masque de profil qui respecte strictement le contour original.
        En mode polygon, applique une verticalisation pixel par pixel.
        Fusionne les polygones qui se chevauchent.

        Args:
            single_mask: Masque binaire initial
            val: Valeur du label à appliquer

        Returns:
            Masque respectant strictement le contour
        """
        try:
            # Créer un masque temporaire pour la fusion
            fusion_mask = np.zeros_like(single_mask)

            # Trouver les contours du masque binaire
            contours, _ = cv2.findContours(single_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)

            if not contours:
                return single_mask

            # Fusionner tous les contours en un seul
            if len(contours) > 1:
                # Remplir tous les contours dans le masque de fusion
                cv2.drawContours(fusion_mask, contours, -1, 1, -1)
                # Trouver le contour externe de la fusion
                contours, _ = cv2.findContours(fusion_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)

            # Créer le masque final
            profile_mask = np.zeros_like(single_mask)

            # Pour chaque contour (maintenant fusionné)
            for contour in contours:
                # Remplir le contour avec la valeur du label
                cv2.fillPoly(profile_mask, [contour], val)

            # En mode polygon, appliquer la verticalisation pixel par pixel
            if self._mask_type_var.get() == "polygon":
                h, w = profile_mask.shape
                # Pour chaque colonne x
                for x in range(w):
                    # Trouver tous les pixels non-nuls dans cette colonne
                    col = profile_mask[:, x]
                    idx = np.where(col == val)[0]
                    if idx.size > 0:
                        # Prendre le plus petit et le plus grand y
                        y_min = idx.min()
                        y_max = idx.max()
                        # Remplir verticalement entre ces points
                        profile_mask[y_min:y_max+1, x] = val

            return profile_mask

        except Exception as e:
            self._logger.error(f"Erreur lors de la génération du masque de profil: {str(e)}")
            return single_mask

    def _generate_profile_mask_for_label(self, single_mask, val, label):
        """
        Génère un masque de profil pour un label spécifique avec ses propres paramètres.

        Args:
            single_mask: Masque binaire initial
            val: Valeur du label à appliquer
            label: Nom du label pour récupérer ses paramètres

        Returns:
            Masque respectant strictement le contour avec paramètres du label
        """
        try:
            # Utiliser les paramètres actuels de l'interface (fonction legacy)
            mask_type = self._mask_type_var.get()

            # Créer un masque temporaire pour la fusion
            fusion_mask = np.zeros_like(single_mask)

            # Trouver les contours du masque binaire
            contours, _ = cv2.findContours(single_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)

            if not contours:
                return single_mask

            # Fusionner tous les contours en un seul
            if len(contours) > 1:
                # Remplir tous les contours dans le masque de fusion
                cv2.drawContours(fusion_mask, contours, -1, 1, -1)
                # Trouver le contour externe de la fusion
                contours, _ = cv2.findContours(fusion_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)

            # Créer le masque final
            profile_mask = np.zeros_like(single_mask)

            # Pour chaque contour (maintenant fusionné)
            for contour in contours:
                # Remplir le contour avec la valeur du label
                cv2.fillPoly(profile_mask, [contour], val)

            # Appliquer la verticalisation seulement si ce label est en mode polygon
            if mask_type == "polygon":
                h, w = profile_mask.shape
                # Pour chaque colonne x
                for x in range(w):
                    # Trouver tous les pixels non-nuls dans cette colonne
                    col = profile_mask[:, x]
                    idx = np.where(col == val)[0]
                    if idx.size > 0:
                        # Prendre le plus petit et le plus grand y
                        y_min = idx.min()
                        y_max = idx.max()
                        # Remplir verticalement entre ces points
                        profile_mask[y_min:y_max+1, x] = val

            return profile_mask

        except Exception as e:
            self._logger.error(f"Erreur lors de la génération du masque de profil pour {label}: {str(e)}")
            return single_mask

    def _remove_isolated_columns(self, x_vals, max_gap=5):
        """Supprime les colonnes isolées dans le masque."""
        if len(x_vals) < 2:
            return x_vals
        diffs = np.diff(x_vals)
        keep = np.ones_like(x_vals, dtype=bool)
        keep[1:] &= diffs <= max_gap
        keep[:-1] &= diffs <= max_gap
        return x_vals[keep]

    def _on_mouse_motion(self, event) -> None:
        """
        Gère le mouvement de la souris pour la prévisualisation.

        Args:
            event: Événement de la souris
        """
        # Mettre à jour la position de la souris pour la prévisualisation du rectangle
        if self._drawing_mode_var.get() == "rectangle" and self._rectangle_start:
            x, y = self._canvas_to_image_coords(
                self._canvas.canvasx(event.x),
                self._canvas.canvasy(event.y)
            )
            self._mouse_pos = (x, y)
            self._draw_polygons()  # Redessiner avec la prévisualisation

    def _free_draw(self, event) -> None:
        """
        Ajoute un point pendant le dessin libre.

        Args:
            event: Événement de la souris
        """
        x, y = self._canvas_to_image_coords(
            self._canvas.canvasx(event.x),
            self._canvas.canvasy(event.y)
        )
        self._current_polygon.append((x, y))
        self._draw_polygons()

    def _start_pan(self, event) -> None:
        """
        Démarre le déplacement de l'image.
        
        Args:
            event: Événement de la souris
        """
        self._canvas.scan_mark(event.x, event.y)

    def _do_pan(self, event) -> None:
        """
        Déplace l'image.
        
        Args:
            event: Événement de la souris
        """
        self._canvas.scan_dragto(event.x, event.y, gain=1)

    def _zoom_image(self, event) -> None:
        """
        Zoom sur l'image.

        Args:
            event: Événement de la souris
        """
        factor = self.ZOOM_FACTOR if event.delta > 0 else 1/self.ZOOM_FACTOR
        self._zoom = max(self.MIN_ZOOM, min(self.MAX_ZOOM, self._zoom * factor))
        # Redessiner avec l'image actuelle (qui peut être un overlay)
        if self._image is not None:
            self.update_image_display(self._image, is_original=False)

    def _switch_label(self, event=None) -> None:
        """Change le label actuel."""
        # Changer de label
        self._controller.switch_label()
        current_label = self._controller.get_current_label()

        # Mettre à jour l'affichage du label
        self._current_label_display.config(
            text=f"Label actuel: {current_label}",
            fg=self.LABEL_COLORS[current_label]
        )

        # Redessiner et mettre à jour le masque
        self._draw_polygons()
        self._schedule_mask_update()

    def _undo_polygon(self, event=None) -> None:
        """
        Annule le dernier polygone.

        Args:
            event: Événement du clavier (optionnel)
        """
        self._controller.undo_polygon()
        # Le cache sera invalidé automatiquement grâce à la clé
        self._draw_polygons()
        self._update_mask_display()

    def _cancel_polygon(self, event=None) -> None:
        """
        Annule le polygone en cours.

        Args:
            event: Événement du clavier (optionnel)
        """
        # Annuler selon le mode de dessin
        self._current_polygon = []
        self._rectangle_start = None
        self._rectangle_preview = None
        self._mouse_pos = None
        self._draw_polygons()

    def _draw_polygons(self) -> None:
        """Dessine tous les polygones sur le canvas."""
        self._canvas.delete("poly")

        # Dessiner les polygones définitifs (colorés)
        for label, polygons in self._controller.get_all_polygons().items():
            for pts in polygons:
                if len(pts) >= 3:
                    coords = [self._image_to_canvas_coords(x, y) for x, y in pts]
                    # Dessiner uniquement la ligne fine du polygone fermé
                    self._canvas.create_line(
                        coords,
                        fill=self.LABEL_COLORS[label],
                        width=2,  # Ligne un peu plus épaisse pour les définitifs
                        tags="poly"
                    )

        # Dessiner les polygones temporaires (gris)
        temp_polygons = self._controller.get_temporary_polygons()
        for temp_polygon in temp_polygons:
            pts = temp_polygon['points']
            if len(pts) >= 3:
                coords = [self._image_to_canvas_coords(x, y) for x, y in pts]
                # Dessiner en gris avec ligne pointillée
                self._canvas.create_line(
                    coords,
                    fill="#808080",  # Gris
                    width=2,
                    dash=(5, 3),  # Ligne pointillée
                    tags="poly"
                )
        
        # Dessiner selon le mode de dessin
        mode = self._drawing_mode_var.get()
        current_label = self._controller.get_current_label()

        if mode == "rectangle":
            # Mode rectangle : dessiner le point de départ et la prévisualisation
            if self._rectangle_start:
                # Dessiner le point de départ
                start_x, start_y = self._rectangle_start
                cx, cy = self._image_to_canvas_coords(start_x, start_y)
                self._canvas.create_oval(
                    cx - self.POINT_RADIUS,
                    cy - self.POINT_RADIUS,
                    cx + self.POINT_RADIUS,
                    cy + self.POINT_RADIUS,
                    fill=self.LABEL_COLORS[current_label],
                    tags="poly"
                )

                # Dessiner la prévisualisation du rectangle si on a la position de la souris
                if hasattr(self, '_mouse_pos') and self._mouse_pos:
                    mouse_x, mouse_y = self._mouse_pos
                    # Créer les coordonnées du rectangle de prévisualisation
                    rect_coords = [
                        self._image_to_canvas_coords(start_x, start_y),
                        self._image_to_canvas_coords(mouse_x, start_y),
                        self._image_to_canvas_coords(mouse_x, mouse_y),
                        self._image_to_canvas_coords(start_x, mouse_y),
                        self._image_to_canvas_coords(start_x, start_y)
                    ]
                    self._canvas.create_line(
                        rect_coords,
                        fill=self.LABEL_COLORS[current_label],
                        dash=self.DASH_PATTERN,
                        tags="poly"
                    )
        else:
            # Mode polygone : dessiner le polygone en cours
            if self._current_polygon:
                coords = [self._image_to_canvas_coords(x, y) for x, y in self._current_polygon]
                if len(coords) >= 2:
                    # Dessiner la ligne en pointillés pour le polygone en cours
                    self._canvas.create_line(
                        coords,
                        fill=self.LABEL_COLORS[current_label],
                        dash=self.DASH_PATTERN,
                        tags="poly"
                    )
                
                # Dessiner les points uniquement pour le polygone en cours
                for x, y in self._current_polygon:
                    cx, cy = self._image_to_canvas_coords(x, y)
                    self._canvas.create_oval(
                        cx - self.POINT_RADIUS,
                        cy - self.POINT_RADIUS,
                        cx + self.POINT_RADIUS,
                        cy + self.POINT_RADIUS,
                        fill=self.LABEL_COLORS[current_label],
                        tags="poly"
                    )

    def load_image(self, image_path: str) -> None:
        """Charge une nouvelle image."""
        try:
            # Charger l'image
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"Impossible de charger l'image: {image_path}")
            
            # Mettre à jour l'affichage
            self.update_image_display(image)
            
            # Mettre à jour le label du nom de fichier
            filename = os.path.basename(image_path)
            self._filename_label.config(text=filename)
            
            # Réinitialiser le zoom et le décalage
            self._zoom = 1.0
            self._offset = [0, 0]
            
            # Réinitialiser le polygone en cours
            self._current_polygon = []
            
            # Mettre à jour le label d'affichage avec la couleur bleue (frontwall)
            self._current_label_display.config(
                text="Label actuel: frontwall",
                fg=self.LABEL_COLORS["frontwall"]
            )
            
            # Redessiner les polygones
            self._draw_polygons()
            
            # Mettre à jour le masque
            self._update_mask_display()
            
        except Exception as e:
            self._logger.error(f"Erreur lors du chargement de l'image: {str(e)}")
            messagebox.showerror("Erreur", f"Impossible de charger l'image: {str(e)}")



    def _on_space_pressed(self, event=None):
        """Gestion de l'appui sur la touche espace"""
        if event and event.char == ' ':
            self._switch_label()

    def _commit_temporary_polygons(self):
        """Valide les polygones temporaires et les ajoute au label actuel."""
        try:
            self._controller.commit_temporary_polygons()
            self._update_temporary_buttons_state()
            self._update_mask_display()
            self._logger.info("Polygones temporaires validés")
        except Exception as e:
            self._logger.error(f"Erreur lors de la validation des polygones: {str(e)}")

    def _cancel_temporary_polygons(self):
        """Annule les polygones temporaires."""
        try:
            self._controller.clear_temporary_polygons()
            self._update_temporary_buttons_state()
            self._update_mask_display()
            self._logger.info("Polygones temporaires annulés")
        except Exception as e:
            self._logger.error(f"Erreur lors de l'annulation des polygones: {str(e)}")

    def _update_temporary_buttons_state(self):
        """Met à jour l'état des boutons selon la présence de polygones temporaires."""
        temp_polygons = self._controller.get_temporary_polygons()
        has_temp = len(temp_polygons) > 0

        # Activer/désactiver les boutons
        state = "normal" if has_temp else "disabled"
        self._commit_button.config(state=state)
        self._cancel_button.config(state=state)

        # Mettre à jour le texte du bouton commit avec le nombre de polygones
        if has_temp:
            current_label = self._controller.get_current_label()
            self._commit_button.config(text=f"Ajouter au {current_label} ({len(temp_polygons)})")
        else:
            self._commit_button.config(text="Ajouter au label")


